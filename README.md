# 🎮 GamePort - 静态游戏门户网站模板

一个高性能、SEO友好、易于维护的静态游戏门户网站模板。支持多主题切换、响应式设计、无需数据库的纯静态部署。

## ✨ 特性

- 🚀 **高性能**: 纯静态HTML，加载速度极快
- 🎨 **多主题支持**: 6种内置主题（默认、复古、赛博朋克、森林、海洋、战斗）
- 📱 **响应式设计**: 完美支持手机、平板、桌面设备
- 🔍 **SEO优化**: 每个游戏页面独立SEO元数据
- 🛠️ **易于维护**: 通过JSON配置文件管理所有内容
- 🎯 **零依赖**: 无需数据库，纯静态部署
- 🔧 **灵活扩展**: 支持YouTube视频、自定义内容块

## 📁 项目结构

```
GamePort/
├── config.json              # 全局配置文件
├── games.json               # 游戏数据文件
├── build.js                 # 构建脚本
├── package.json             # 项目配置
├── src/                     # 源文件目录
│   ├── templates/           # HTML模板
│   │   ├── _main-layout-template.html      # 主布局模板
│   │   ├── game-detail-template.html       # 游戏详情页模板
│   │   └── _list-layout-template.html      # 列表页模板
│   ├── components/          # 组件模板
│   │   ├── game-card.html   # 游戏卡片组件
│   │   └── feature-blocks.html # 功能块组件
│   ├── css/                 # 样式文件
│   │   ├── style.css        # 主样式文件
│   │   └── themes/          # 主题样式
│   ├── js/                  # JavaScript文件
│   └── images/              # 图片资源
└── dist/                    # 构建输出目录
```

## 🚀 快速开始

### 环境要求

- Node.js >= 14.0.0
- 无需其他依赖

### 安装和使用

1. **克隆或下载模板**
```bash
# 下载模板到本地
git clone <repository-url> my-game-site
cd my-game-site
```

2. **配置网站信息**

编辑 `config.json` 文件，这是网站的全局配置文件：

```json
{
  // 网站基本信息
  "site_name": "你的游戏网站名称",              // 显示在页面标题、导航栏等位置
  "site_url": "https://yourdomain.com",        // 你的网站域名，用于SEO和链接生成

  // 主题配置
  "available_themes": [                        // 可用的主题列表，不要修改
    "default", "retro", "cyberpunk",
    "forest", "ocean", "battle"
  ],
  "selected_theme": "cyberpunk",               // 当前使用的主题，从上面列表中选择

  // 构建设置（一般不需要修改）
  "build_settings": {
    "output_dir": "dist",                      // 构建输出目录
    "copy_assets": true,                       // 是否复制静态资源
    "minify_css": false,                       // 是否压缩CSS
    "minify_js": false                         // 是否压缩JavaScript
  },

  // SEO优化设置
  "seo": {
    "default_title": "你的网站 - 免费在线游戏",    // 默认页面标题
    "default_description": "在这里玩最好的免费在线游戏...", // 默认页面描述
    "keywords": "在线游戏,免费游戏,网页游戏",        // 网站关键词，用逗号分隔
    "author": "你的团队名称",                    // 网站作者
    "language": "zh-CN",                       // 网站语言（zh-CN中文，en英文）
    "canonical_base_url": "https://yourdomain.com" // 规范化URL，与site_url保持一致
  },

  // 游戏iframe显示设置
  "iframe_settings": {
    "default_size": "small",                   // 默认游戏窗口大小（small或large）
    "sizes": {
      "small": {                               // 小尺寸设置
        "width": 854,                          // 游戏窗口宽度
        "height": 480,                         // 游戏窗口高度
        "container_width": "1294px"            // 容器宽度
      },
      "large": {                               // 大尺寸设置
        "width": 960,
        "height": 540,
        "container_width": "1400px"
      }
    }
  },

  // 首页显示设置
  "display_settings": {
    "homepage": {
      "new_games_display": {
        "count": 8,                            // 首页"新游戏"区域显示的游戏数量
        "selection_method": "configured",       // 选择方式（固定为configured）
        "selected_games": [                    // 要显示的游戏ID列表
          "game-id-1", "game-id-2", "..."     // 对应games.json中的游戏id
        ]
      },
      "popular_games_display": {
        "count": 6,                            // 首页"热门游戏"区域显示的游戏数量
        "columns": 2,                          // 显示列数
        "selection_method": "configured",
        "selected_games": [                    // 要显示的游戏ID列表
          "game-id-1", "game-id-2", "..."
        ]
      }
    }
  }
}
```

**重要配置项说明：**
- `site_name`: 会显示在网站标题、导航栏、FAQ等所有位置
- `site_url`: 必须是你的实际域名，影响SEO和分享链接
- `selected_theme`: 从6个主题中选择一个
- `seo.language`: 中文网站用"zh-CN"，英文网站用"en"
- `display_settings`: 控制首页显示哪些游戏，游戏ID必须在games.json中存在

3. **添加游戏数据**

编辑 `games.json` 文件，这是所有游戏内容的数据源：

**重要：数组第一个对象是首页特殊游戏，从第二个对象开始是普通游戏列表**

```json
[
  {
    // 首页特殊游戏（数组第一个对象，必须存在）
    "id": "homepage-special",                  // 游戏唯一标识符，不能重复
    "name": "首页特色游戏名称",                  // 游戏显示名称
    "category": "popular",                     // 游戏分类（popular或new）
    "thumbnail": "images/games/featured.jpg",   // 游戏缩略图路径
    "gameUrl": "https://your-game-url.com",    // 实际游戏链接

    // 游戏详情页的功能块（可选）
    "feature_blocks": [
      {
        // YouTube视频块
        "type": "youtube",
        "videoId": "dQw4w9WgXcQ",             // YouTube视频ID（从URL中提取）
        "title": "游戏演示视频"                // 视频标题
      },
      {
        // 文本内容块 - 段落格式
        "type": "section",
        "title": "游戏介绍",                   // 内容块标题
        "content": "这是一个精彩的游戏，提供丰富的游戏体验...", // 内容文本
        "format": "paragraph"                 // 格式：paragraph（段落）
      },
      {
        // 文本内容块 - 列表格式
        "type": "section",
        "title": "游戏玩法",
        "content": "使用方向键移动角色|按空格键跳跃|收集金币获得分数|避开敌人和障碍物", // 用|分隔的列表项
        "format": "list"                      // 格式：list（列表）
      }
    ],

    // SEO元数据（可选）
    "meta": {
      "title": "自定义页面标题",               // 覆盖默认SEO标题
      "description": "自定义页面描述"         // 覆盖默认SEO描述
    }
  },

  {
    // 普通游戏（从第二个对象开始）
    "id": "super-mario-bros",                 // 游戏唯一ID
    "name": "Super Mario Bros",               // 游戏名称
    "category": "popular",                    // 分类：popular（热门）或new（新游戏）
    "thumbnail": "images/games/mario.jpg",    // 缩略图路径
    "gameUrl": "https://mario-game-url.com",  // 游戏链接

    // feature_blocks和meta字段可选，结构同上
    "feature_blocks": [...],                  // 可选：游戏详情页内容
    "meta": {...}                            // 可选：SEO元数据
  }
]
```

**feature_blocks详细说明：**

1. **YouTube视频块**：
   ```json
   {
     "type": "youtube",
     "videoId": "dQw4w9WgXcQ",    // 从YouTube URL提取：https://www.youtube.com/watch?v=dQw4w9WgXcQ
     "title": "视频标题"
   }
   ```

2. **文本内容块 - 段落格式**：
   ```json
   {
     "type": "section",
     "title": "标题",
     "content": "这里是段落文本，会显示为普通段落。",
     "format": "paragraph"        // 显示为段落文本
   }
   ```

3. **文本内容块 - 列表格式**：
   ```json
   {
     "type": "section",
     "title": "标题",
     "content": "第一项|第二项|第三项|第四项",  // 用|符号分隔每个列表项
     "format": "list"             // 显示为有序列表
   }
   ```

**重要注意事项：**
- 游戏ID必须唯一，不能重复
- 缩略图路径要确保文件存在
- category只能是"popular"或"new"
- format只能是"paragraph"或"list"
- 列表格式的content用"|"分隔每个项目
- 首页显示的游戏ID必须在config.json的display_settings中配置

4. **构建网站**
```bash
# 构建静态网站
npm run build

# 或者使用开发模式（构建后显示提示）
npm run dev
```

5. **部署网站**
将项目推送到Git仓库，然后连接到Cloudflare Pages或Vercel进行自动部署。详见[部署章节](#📦-部署)。

## 📝 配置说明

### config.json 配置

| 字段 | 说明 | 示例 |
|------|------|------|
| `site_name` | 网站名称 | "GamePort" |
| `site_url` | 网站URL | "https://gameport.com" |
| `selected_theme` | 当前主题 | "cyberpunk" |
| `available_themes` | 可用主题列表 | ["default", "retro", ...] |
| `seo.default_title` | 默认SEO标题 | "GamePort - 免费在线游戏" |
| `seo.default_description` | 默认SEO描述 | "玩最好的免费在线游戏..." |
| `seo.keywords` | 网站关键词 | "在线游戏,免费游戏" |
| `seo.language` | 网站语言 | "zh-CN" 或 "en" |
| `iframe_settings.default_size` | 默认游戏窗口大小 | "small" 或 "large" |
| `display_settings.homepage` | 首页游戏显示配置 | 控制显示哪些游戏 |

### games.json 数据结构

#### 首页特殊游戏（数组第一个对象）
```json
{
  "id": "homepage-special",
  "name": "首页展示的游戏名称",
  "category": "popular",
  "thumbnail": "images/games/featured.jpg",
  "gameUrl": "实际游戏链接",
  "feature_blocks": [
    {
      "type": "youtube",
      "videoId": "YouTube视频ID",
      "title": "视频标题"
    },
    {
      "type": "section", 
      "title": "内容块标题",
      "content": "内容块描述文字"
    }
  ]
}
```

#### 普通游戏（数组其余对象）
```json
{
  "id": "game-id",
  "name": "游戏名称",
  "category": "popular|new",
  "thumbnail": "images/games/game.jpg",
  "gameUrl": "游戏链接",
  "feature_blocks": [...] // 可选
}
```

## 🎨 主题系统

### 内置主题

- `default` - 默认主题
- `retro` - 复古风格
- `cyberpunk` - 赛博朋克风格
- `forest` - 森林主题
- `ocean` - 海洋主题
- `battle` - 战斗主题

### 切换主题

在 `config.json` 中修改 `selected_theme` 字段：
```json
{
  "selected_theme": "cyberpunk"
}
```

然后重新运行构建命令。

### 自定义主题

1. 在 `src/css/themes/` 目录下创建新的主题文件：`theme-mytheme.css`
2. 在 `config.json` 的 `available_themes` 数组中添加 `"mytheme"`
3. 设置 `selected_theme` 为 `"mytheme"`

## 🔧 高级功能

### Feature Blocks 内容块

支持多种类型的内容块：

#### YouTube视频块
```json
{
  "type": "youtube",
  "videoId": "dQw4w9WgXcQ",
  "title": "游戏演示视频"
}
```

#### 文本内容块
```json
{
  "type": "section",
  "title": "游戏介绍",
  "content": "这是一个精彩的游戏..."
}
```

### SEO优化

- 每个游戏页面自动生成独立的SEO元数据
- 支持Open Graph和Twitter Card
- 自动生成sitemap（可扩展）
- 语义化HTML结构

### 响应式设计

- 移动优先设计
- 支持手机、平板、桌面设备
- 自适应游戏卡片布局
- 触摸友好的交互设计

## 📦 部署

### 推荐部署平台

本模板支持源码直接部署，无需本地构建。推荐使用以下平台：

#### 🔥 Cloudflare Pages（推荐）

**优势**: 免费、快速、全球CDN、自动HTTPS

**部署步骤**:
1. 将项目代码推送到GitHub/GitLab仓库
2. 登录 [Cloudflare Pages](https://pages.cloudflare.com/)
3. 点击"创建项目" → "连接到Git"
4. 选择你的仓库
5. 配置构建设置：
   - **构建命令**: `npm run build`
   - **构建输出目录**: `dist`
   - **Node.js版本**: `18` 或更高
6. 点击"保存并部署"

**自动部署**: 每次推送代码到仓库，Cloudflare Pages会自动重新构建和部署。

#### ⚡ Vercel（推荐）

**优势**: 零配置、自动优化、边缘网络

**部署步骤**:
1. 将项目代码推送到GitHub/GitLab/Bitbucket仓库
2. 登录 [Vercel](https://vercel.com/)
3. 点击"New Project"
4. 导入你的Git仓库
5. Vercel会自动检测到这是Node.js项目并配置：
   - **构建命令**: `npm run build`
   - **输出目录**: `dist`
6. 点击"Deploy"

**自动部署**: 每次推送到主分支会自动触发重新部署。

### 本地构建部署（备选方案）

如果需要手动部署到其他平台：

```bash
# 本地构建
npm run build

# 将 dist/ 目录内容上传到你的Web服务器
```

## 🛠️ 开发指南

### 修改模板

- 主页模板：`src/templates/_main-layout-template.html`
- 游戏详情页：`src/templates/game-detail-template.html`
- 列表页模板：`src/templates/_list-layout-template.html`

### 添加新组件

1. 在 `src/components/` 创建组件HTML文件
2. 在 `build.js` 中添加组件处理逻辑
3. 在模板中使用占位符引用组件

### 自定义样式

- 主样式：`src/css/style.css`
- 主题样式：`src/css/themes/theme-*.css`
- 使用CSS变量系统便于主题切换

## 📋 构建脚本

### 可用命令

```bash
npm run build    # 构建生产版本
npm run dev      # 开发模式构建
npm run clean    # 清理输出目录
```

### 构建过程

1. 读取 `config.json` 和 `games.json`
2. 处理模板占位符替换
3. 生成游戏卡片和功能块
4. 复制静态资源（CSS、JS、图片）
5. 输出到 `dist/` 目录

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个模板！

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🆘 支持

如有问题，请查看：
- 项目文档：`PRD.md` 和 `BUILD_PLAN.md`
- 提交Issue获取帮助
- 查看示例配置文件
