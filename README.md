# 🎮 GamePort - 静态游戏门户网站模板

一个高性能、SEO友好、易于维护的静态游戏门户网站模板。支持多主题切换、响应式设计、无需数据库的纯静态部署。

## ✨ 特性

- 🚀 **高性能**: 纯静态HTML，加载速度极快
- 🎨 **多主题支持**: 6种内置主题（默认、复古、赛博朋克、森林、海洋、战斗）
- 📱 **响应式设计**: 完美支持手机、平板、桌面设备
- 🔍 **SEO优化**: 每个游戏页面独立SEO元数据
- 🛠️ **易于维护**: 通过JSON配置文件管理所有内容
- 🎯 **零依赖**: 无需数据库，纯静态部署
- 🔧 **灵活扩展**: 支持YouTube视频、自定义内容块

## 📁 项目结构

```
GamePort/
├── config.json              # 全局配置文件
├── games.json               # 游戏数据文件
├── build.js                 # 构建脚本
├── package.json             # 项目配置
├── src/                     # 源文件目录
│   ├── templates/           # HTML模板
│   │   ├── _main-layout-template.html      # 主布局模板
│   │   ├── game-detail-template.html       # 游戏详情页模板
│   │   └── _list-layout-template.html      # 列表页模板
│   ├── components/          # 组件模板
│   │   ├── game-card.html   # 游戏卡片组件
│   │   └── feature-blocks.html # 功能块组件
│   ├── css/                 # 样式文件
│   │   ├── style.css        # 主样式文件
│   │   └── themes/          # 主题样式
│   ├── js/                  # JavaScript文件
│   └── images/              # 图片资源
└── dist/                    # 构建输出目录
```

## 🚀 快速开始

### 环境要求

- Node.js >= 14.0.0
- 无需其他依赖

### 安装和使用

1. **克隆或下载模板**
```bash
# 下载模板到本地
git clone <repository-url> my-game-site
cd my-game-site
```

2. **配置网站信息**
编辑 `config.json` 文件：
```json
{
  "site_name": "你的游戏网站名称",
  "selected_theme": "cyberpunk",
  "site_description": "网站描述",
  "site_url": "https://yourdomain.com"
}
```

3. **添加游戏数据**
编辑 `games.json` 文件添加你的游戏：
```json
[
  {
    "id": "homepage-special",
    "name": "首页特色游戏",
    "category": "popular",
    "thumbnail": "images/games/game1.jpg",
    "gameUrl": "https://your-game-url.com",
    "feature_blocks": [...]
  },
  {
    "id": "super-mario-bros",
    "name": "Super Mario Bros",
    "category": "popular",
    "thumbnail": "images/games/mario.jpg",
    "gameUrl": "https://mario-game-url.com"
  }
]
```

4. **构建网站**
```bash
# 构建静态网站
npm run build

# 或者使用开发模式（构建后显示提示）
npm run dev
```

5. **部署网站**
将 `dist/` 目录中的所有文件上传到你的Web服务器即可。

## 📝 配置说明

### config.json 配置

| 字段 | 说明 | 示例 |
|------|------|------|
| `site_name` | 网站名称 | "GamePort" |
| `selected_theme` | 当前主题 | "cyberpunk" |
| `available_themes` | 可用主题列表 | ["default", "retro", ...] |
| `site_description` | 网站描述 | "最好的在线游戏平台" |
| `site_url` | 网站URL | "https://gameport.com" |

### games.json 数据结构

#### 首页特殊游戏（数组第一个对象）
```json
{
  "id": "homepage-special",
  "name": "首页展示的游戏名称",
  "category": "popular",
  "thumbnail": "images/games/featured.jpg",
  "gameUrl": "实际游戏链接",
  "feature_blocks": [
    {
      "type": "youtube",
      "videoId": "YouTube视频ID",
      "title": "视频标题"
    },
    {
      "type": "section", 
      "title": "内容块标题",
      "content": "内容块描述文字"
    }
  ]
}
```

#### 普通游戏（数组其余对象）
```json
{
  "id": "game-id",
  "name": "游戏名称",
  "category": "popular|new",
  "thumbnail": "images/games/game.jpg",
  "gameUrl": "游戏链接",
  "feature_blocks": [...] // 可选
}
```

## 🎨 主题系统

### 内置主题

- `default` - 默认主题
- `retro` - 复古风格
- `cyberpunk` - 赛博朋克风格
- `forest` - 森林主题
- `ocean` - 海洋主题
- `battle` - 战斗主题

### 切换主题

在 `config.json` 中修改 `selected_theme` 字段：
```json
{
  "selected_theme": "cyberpunk"
}
```

然后重新运行构建命令。

### 自定义主题

1. 在 `src/css/themes/` 目录下创建新的主题文件：`theme-mytheme.css`
2. 在 `config.json` 的 `available_themes` 数组中添加 `"mytheme"`
3. 设置 `selected_theme` 为 `"mytheme"`

## 🔧 高级功能

### Feature Blocks 内容块

支持多种类型的内容块：

#### YouTube视频块
```json
{
  "type": "youtube",
  "videoId": "dQw4w9WgXcQ",
  "title": "游戏演示视频"
}
```

#### 文本内容块
```json
{
  "type": "section",
  "title": "游戏介绍",
  "content": "这是一个精彩的游戏..."
}
```

### SEO优化

- 每个游戏页面自动生成独立的SEO元数据
- 支持Open Graph和Twitter Card
- 自动生成sitemap（可扩展）
- 语义化HTML结构

### 响应式设计

- 移动优先设计
- 支持手机、平板、桌面设备
- 自适应游戏卡片布局
- 触摸友好的交互设计

## 📦 部署

### 静态托管平台

推荐部署到以下平台：

- **Netlify**: 拖拽 `dist` 文件夹即可
- **Vercel**: 连接Git仓库自动部署
- **GitHub Pages**: 上传到 `gh-pages` 分支
- **传统Web服务器**: 上传 `dist` 目录内容

### 自动化部署

可以设置GitHub Actions等CI/CD工具：

1. 代码推送触发构建
2. 自动运行 `npm run build`
3. 部署 `dist` 目录到服务器

## 🛠️ 开发指南

### 修改模板

- 主页模板：`src/templates/_main-layout-template.html`
- 游戏详情页：`src/templates/game-detail-template.html`
- 列表页模板：`src/templates/_list-layout-template.html`

### 添加新组件

1. 在 `src/components/` 创建组件HTML文件
2. 在 `build.js` 中添加组件处理逻辑
3. 在模板中使用占位符引用组件

### 自定义样式

- 主样式：`src/css/style.css`
- 主题样式：`src/css/themes/theme-*.css`
- 使用CSS变量系统便于主题切换

## 📋 构建脚本

### 可用命令

```bash
npm run build    # 构建生产版本
npm run dev      # 开发模式构建
npm run clean    # 清理输出目录
```

### 构建过程

1. 读取 `config.json` 和 `games.json`
2. 处理模板占位符替换
3. 生成游戏卡片和功能块
4. 复制静态资源（CSS、JS、图片）
5. 输出到 `dist/` 目录

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个模板！

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🆘 支持

如有问题，请查看：
- 项目文档：`PRD.md` 和 `BUILD_PLAN.md`
- 提交Issue获取帮助
- 查看示例配置文件
